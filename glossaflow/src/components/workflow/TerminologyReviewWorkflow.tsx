'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  User,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Edit
} from 'lucide-react';
import {
  useGetTerminologyQuery,
  useApproveTerminologyEntryMutation,
  useRejectTerminologyEntryMutation,
  type TerminologyEntry
} from '@/lib/api/terminology';

interface TerminologyReviewWorkflowProps {
  projectId: string;
  onWorkflowComplete?: () => void;
  className?: string;
}

interface ReviewAction {
  action: 'approve' | 'reject' | 'request_changes';
  notes?: string;
  suggestedChanges?: {
    targetTerm?: string;
    category?: string;
    usageNotes?: string;
  };
}

export function TerminologyReviewWorkflow({
  projectId,
  onWorkflowComplete,
  className = ''
}: TerminologyReviewWorkflowProps) {
  const [currentTermIndex, setCurrentTermIndex] = useState(0);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch pending terminology entries
  const {
    data: terminologyData,
    error: terminologyError,
    isLoading: terminologyLoading,
    refetch: refetchTerminology
  } = useGetTerminologyQuery({
    projectId,
    approvalStatus: 'pending',
    page: 1,
    limit: 100
  });

  // Mutations
  const [approveTerminologyEntry] = useApproveTerminologyEntryMutation();
  const [rejectTerminologyEntry] = useRejectTerminologyEntryMutation();

  const pendingTerms = terminologyData?.data?.items || [];
  const workflowStats = {
    total: pendingTerms.length,
    reviewed: currentTermIndex,
    approved: 0, // This would need to be tracked separately
    rejected: 0, // This would need to be tracked separately
    pending: pendingTerms.length - currentTermIndex,
  };

  const handleReviewAction = async (action: ReviewAction) => {
    if (currentTermIndex >= pendingTerms.length) return;

    const currentTerm = pendingTerms[currentTermIndex];
    if (!currentTerm) return;

    setIsSubmitting(true);

    try {
      if (action.action === 'approve') {
        await approveTerminologyEntry(currentTerm.id).unwrap();
      } else if (action.action === 'reject') {
        await rejectTerminologyEntry(currentTerm.id).unwrap();
      }

      // Refresh data
      await refetchTerminology();

      // Move to next term
      if (currentTermIndex < pendingTerms.length - 1) {
        setCurrentTermIndex(prev => prev + 1);
        setReviewNotes('');
      } else {
        // Workflow complete
        if (onWorkflowComplete) {
          onWorkflowComplete();
        }
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentTerm = pendingTerms[currentTermIndex];
  const isWorkflowComplete = currentTermIndex >= pendingTerms.length;
  const progress = pendingTerms.length > 0 ? (workflowStats.reviewed / pendingTerms.length) * 100 : 0;

  // Show loading state
  if (terminologyLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5 text-blue-500" />
            Loading Terminology Review...
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-32 w-full" />
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (terminologyError) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
            Error Loading Terminology
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load pending terminology entries. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (pendingTerms.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
            Terminology Review Complete
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              All terminology has been reviewed and approved. Translation work can begin.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isWorkflowComplete) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
            Review Workflow Complete
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Terminology review workflow has been completed. Translation can now begin.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{workflowStats.approved}</div>
              <div className="text-sm text-gray-600">Approved</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{workflowStats.rejected}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{workflowStats.total}</div>
              <div className="text-sm text-gray-600">Total Reviewed</div>
            </div>
          </div>

          <Button onClick={onWorkflowComplete} className="w-full">
            Continue to Translation
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-blue-500" />
              Terminology Review Workflow
            </CardTitle>
            <Badge variant="outline">
              {currentTermIndex + 1} of {pendingTerms.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-xs text-gray-600">
              <span>{workflowStats.approved} approved</span>
              <span>{workflowStats.rejected} rejected</span>
              <span>{workflowStats.pending} pending</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Term Review */}
      {currentTerm && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Review Term</CardTitle>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">{currentTerm.category}</Badge>
                <Badge variant="secondary">
                  {new Date(currentTerm.createdAt).toLocaleDateString()}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Term Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Source Term</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                    <p className="font-medium">{currentTerm.sourceTerm}</p>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Target Translation</label>
                  <div className="mt-1 p-3 bg-blue-50 rounded-lg">
                    <p className="font-medium text-blue-900">{currentTerm.targetTerm}</p>
                  </div>
                </div>

                {currentTerm.context && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Context</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm">{currentTerm.context}</p>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {currentTerm.usageNotes && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Usage Notes</label>
                    <div className="mt-1 p-3 bg-yellow-50 rounded-lg">
                      <p className="text-sm">{currentTerm.usageNotes}</p>
                    </div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-700">Submitted</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">
                      {new Date(currentTerm.createdAt).toLocaleDateString()} at{' '}
                      {new Date(currentTerm.createdAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Notes */}
            <div>
              <label className="text-sm font-medium text-gray-700">Review Notes (Optional)</label>
              <Textarea
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add notes about your review decision..."
                className="mt-1"
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-4 border-t">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleReviewAction({ 
                    action: 'request_changes', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Request Changes
                </Button>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleReviewAction({ 
                    action: 'reject', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  <ThumbsDown className="mr-2 h-4 w-4" />
                  Reject
                </Button>
                <Button
                  onClick={() => handleReviewAction({ 
                    action: 'approve', 
                    notes: reviewNotes 
                  })}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <ThumbsUp className="mr-2 h-4 w-4" />
                  )}
                  Approve
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Workflow Instructions */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> All terminology must be reviewed and approved before translation work can begin. 
          This ensures consistency across the entire project series.
        </AlertDescription>
      </Alert>
    </div>
  );
}
