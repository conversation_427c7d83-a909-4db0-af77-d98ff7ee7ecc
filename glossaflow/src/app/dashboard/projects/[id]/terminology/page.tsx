'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TerminologyReviewWorkflow } from '@/components/workflow/TerminologyReviewWorkflow';
import {
  BookOpen,
  Plus,
  Search,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
  RefreshCw
} from 'lucide-react';
import { useGetProjectQuery } from '@/store/api/projectsApi';
import {
  useGetTerminologyQuery,
  useGetTerminologyStatsQuery,
  useCreateTerminologyEntryMutation,
  useApproveTerminologyEntryMutation,
  useRejectTerminologyEntryMutation,
  type TerminologyEntry
} from '@/lib/api/terminology';
import { CreateTermDialog } from '@/components/terminology/CreateTermDialog';

export default function ProjectTerminologyPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const { data: session, status: sessionStatus } = useSession();

  const [activeTab, setActiveTab] = useState('workflow');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Skip API calls until session is ready
  const skipQueries = sessionStatus === 'loading' || !session;

  // Fetch project data
  const {
    data: projectData,
    error: projectError,
    isLoading: projectLoading,
    refetch: refetchProject
  } = useGetProjectQuery(projectId, {
    skip: skipQueries
  });

  // Fetch terminology stats
  const {
    data: terminologyStatsData,
    error: terminologyStatsError,
    isLoading: terminologyStatsLoading,
    refetch: refetchTerminologyStats
  } = useGetTerminologyStatsQuery(undefined, {
    skip: skipQueries
  });

  // Fetch terminology entries with filters
  const {
    data: terminologyData,
    error: terminologyError,
    isLoading: terminologyLoading,
    refetch: refetchTerminology
  } = useGetTerminologyQuery({
    projectId,
    search: searchQuery || undefined,
    category: filterCategory !== 'all' ? filterCategory : undefined,
    approvalStatus: filterStatus !== 'all' ? filterStatus as TerminologyEntry['approvalStatus'] : undefined,
    page: 1,
    limit: 50
  }, {
    skip: skipQueries
  });

  // Mutations
  const [createTerminologyEntry] = useCreateTerminologyEntryMutation();
  const [approveTerminologyEntry] = useApproveTerminologyEntryMutation();
  const [rejectTerminologyEntry] = useRejectTerminologyEntryMutation();

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    if (sessionStatus !== 'authenticated' || !session) {
      return;
    }

    const interval = setInterval(() => {
      refetchProject();
      refetchTerminologyStats();
      refetchTerminology();
    }, 30000);

    return () => clearInterval(interval);
  }, [sessionStatus, session, refetchProject, refetchTerminologyStats, refetchTerminology]);

  // Handle workflow completion
  const handleWorkflowComplete = () => {
    console.log('Terminology workflow completed');
    setActiveTab('overview');
    refetchTerminology();
    refetchTerminologyStats();
  };

  // Handle terminology actions
  const handleCreateTerm = async (data: {
    sourceTerm: string;
    targetTerm: string;
    targetLanguage: string;
    category: string;
    context?: string;
    usageNotes?: string;
  }) => {
    try {
      await createTerminologyEntry({
        sourceTerm: data.sourceTerm,
        targetTerm: data.targetTerm,
        targetLanguage: data.targetLanguage,
        category: data.category,
        context: data.context,
        usageNotes: data.usageNotes,
        projectId: projectId,
      }).unwrap();

      setIsCreateDialogOpen(false);
      refetchTerminology();
      refetchTerminologyStats();
    } catch (error) {
      console.error('Failed to create terminology:', error);
    }
  };

  const handleApprove = async (termId: string) => {
    try {
      await approveTerminologyEntry(termId).unwrap();
      refetchTerminology();
      refetchTerminologyStats();
    } catch (error) {
      console.error('Failed to approve terminology:', error);
    }
  };

  const handleReject = async (termId: string) => {
    try {
      await rejectTerminologyEntry(termId).unwrap();
      refetchTerminology();
      refetchTerminologyStats();
    } catch (error) {
      console.error('Failed to reject terminology:', error);
    }
  };

  const handleEdit = (term: TerminologyEntry) => {
    // TODO: Open edit modal
    console.log('Edit term:', term);
  };

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'character': return 'bg-purple-100 text-purple-800';
      case 'location': return 'bg-green-100 text-green-800';
      case 'item': return 'bg-blue-100 text-blue-800';
      case 'concept': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Extract data from API responses
  const project = projectData?.project;
  const terminologyStats = terminologyStatsData?.data || {
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  };
  const terminology = terminologyData?.data?.items || [];

  // Loading states
  const isLoading = projectLoading || terminologyStatsLoading || terminologyLoading;
  const hasError = projectError || terminologyStatsError || terminologyError;

  // Show loading skeleton while data is loading
  if (sessionStatus === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-32" />
              <div>
                <Skeleton className="h-8 w-64 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>
            <Skeleton className="h-10 w-24" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load terminology data.
              <Button
                variant="link"
                className="p-0 ml-2 h-auto"
                onClick={() => {
                  refetchProject();
                  refetchTerminologyStats();
                  refetchTerminology();
                }}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Try again
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push(`/dashboard/projects/${projectId}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-3xl font-bold">{project?.name || 'Project'}</h1>
              <p className="text-gray-600">Terminology Management</p>
            </div>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Term
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Terms</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-gray-900">{terminologyStats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="workflow">Review Workflow</TabsTrigger>
            <TabsTrigger value="overview">All Terms</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="workflow" className="space-y-4">
            <TerminologyReviewWorkflow
              projectId={projectId}
              onWorkflowComplete={handleWorkflowComplete}
            />
          </TabsContent>

          <TabsContent value="overview" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-wrap gap-4">
                  <div className="flex-1 min-w-[200px]">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search terms..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="character">Character</SelectItem>
                      <SelectItem value="location">Location</SelectItem>
                      <SelectItem value="item">Item</SelectItem>
                      <SelectItem value="concept">Concept</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Terminology List */}
            <div className="space-y-3">
              {terminology.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <BookOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No terminology found</h3>
                    <p className="text-gray-600 mb-4">
                      {searchQuery || filterCategory !== 'all' || filterStatus !== 'all'
                        ? 'No terms match your current filters. Try adjusting your search criteria.'
                        : 'Start building your terminology database by adding your first term.'
                      }
                    </p>
                    <Button onClick={() => setIsCreateDialogOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Term
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                terminology.map((term) => (
                  <Card key={term.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-3">
                            <h4 className="font-medium">&quot;{term.sourceTerm}&quot;</h4>
                            <span className="text-gray-400">→</span>
                            <span className="text-blue-600 font-medium">{term.targetTerm}</span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Badge className={getCategoryColor(term.category)}>
                              {term.category}
                            </Badge>
                            <Badge className={getStatusColor(term.approvalStatus)}>
                              {term.approvalStatus}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              Used {term.frequency} times
                            </span>
                          </div>

                          {term.context && (
                            <p className="text-sm text-gray-600">{term.context}</p>
                          )}

                          {term.usageNotes && (
                            <p className="text-xs text-gray-500 italic">{term.usageNotes}</p>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(term)}
                          >
                            Edit
                          </Button>
                          {term.approvalStatus === 'pending' && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleReject(term.id)}
                              >
                                Reject
                              </Button>
                              <Button
                                size="sm"
                                onClick={() => handleApprove(term.id)}
                              >
                                Approve
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Terminology Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">Analytics and insights coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Create Term Dialog */}
        <CreateTermDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreateTerm}
        />
      </div>
    </DashboardLayout>
  );
}
