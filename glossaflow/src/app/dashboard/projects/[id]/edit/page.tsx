'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useState } from 'react';
import { <PERSON><PERSON>ef<PERSON>, Loader2, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectForm } from '@/components/projects/ProjectForm';
import { ProjectFormData } from '@/components/projects/CreateProjectDialog';
import { useGetProjectQuery, useUpdateProjectMutation } from '@/lib/api/projects';

export default function EditProjectPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Fetch project data
  const {
    data: projectResponse,
    error: projectError,
    isLoading: projectLoading,
    refetch: refetchProject
  } = useGetProjectQuery(projectId);

  // Update project mutation
  const [updateProject, { isLoading: isUpdating }] = useUpdateProjectMutation();

  const project = projectResponse?.project;

  const handleSubmit = async (formData: ProjectFormData) => {
    try {
      setMessage(null);
      
      // Convert form data to API format
      const updateData = {
        name: formData.name,
        description: formData.description || undefined,
        source_language: formData.sourceLanguage,
        target_languages: formData.targetLanguages,
        due_date: formData.deadline ? formData.deadline.toISOString() : undefined,
        priority: formData.priority,
        document_type: formData.documentType,
      };

      await updateProject({
        id: projectId,
        data: updateData
      }).unwrap();

      setMessage({
        type: 'success',
        text: 'Project updated successfully!'
      });

      // Optionally redirect back to project detail page after a delay
      setTimeout(() => {
        router.push(`/dashboard/projects/${projectId}`);
      }, 1500);

    } catch (error: any) {
      console.error('Failed to update project:', error);
      setMessage({
        type: 'error',
        text: error?.data?.error || 'Failed to update project. Please try again.'
      });
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/projects/${projectId}`);
  };

  // Loading state
  if (projectLoading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <p className="text-gray-600">Loading project details...</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (projectError || !project) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Project Not Found</h2>
                <p className="text-gray-600 mt-2">
                  The project you're looking for doesn't exist or you don't have permission to edit it.
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/projects')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Projects
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(`/dashboard/projects/${projectId}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Edit Project</h1>
              <p className="text-gray-600">Update project details and settings</p>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* Edit Form */}
        <Card>
          <CardHeader>
            <CardTitle>Project Information</CardTitle>
            <CardDescription>
              Update the basic information and settings for "{project.name}"
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ProjectForm
              project={project}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              isLoading={isUpdating}
              submitButtonText="Update Project"
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
