import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // First, check if user has access to this project
    const { data: projectAccess, error: accessError } = await supabase
      .from('project_members')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', userId)
      .single();

    // Also check if user is creator
    const { data: projectOwnership, error: ownershipError } = await supabase
      .from('projects')
      .select('id, created_by')
      .eq('id', projectId)
      .eq('created_by', userId)
      .single();



    if (!projectAccess && !projectOwnership) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Get project basic data
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (projectError) {
      console.error('Project fetch error:', projectError);
      return NextResponse.json(
        { error: 'Project not found', success: false },
        { status: 404 }
      );
    }

    // Get project team members (without user join due to foreign key issues)
    const { data: teamMembers, error: teamError } = await supabase
      .from('project_members')
      .select('*')
      .eq('project_id', projectId);

    if (teamError) {
      console.error('Team members fetch error:', teamError);
    }

    // Get user details separately for team members
    let teamMembersWithUsers = [];
    if (teamMembers && teamMembers.length > 0) {
      const userIds = teamMembers.map(member => member.user_id);
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, name, email, avatar_url')
        .in('id', userIds);

      if (!usersError && users) {
        teamMembersWithUsers = teamMembers.map(member => ({
          ...member,
          user: users.find(user => user.id === member.user_id) || null
        }));
      } else {
        console.error('Users fetch error:', usersError);
        teamMembersWithUsers = teamMembers.map(member => ({
          ...member,
          user: null
        }));
      }
    }

    // Get project files/documents
    const { data: documents, error: documentsError } = await supabase
      .from('source_documents')
      .select('*')
      .eq('project_id', projectId);

    if (documentsError) {
      console.error('Documents fetch error:', documentsError);
    }

    // Get project progress stats
    const { data: progressStats, error: progressError } = await supabase
      .from('translation_segments')
      .select('status')
      .eq('project_id', projectId);

    if (progressError) {
      console.error('Progress stats fetch error:', progressError);
    }

    // Calculate progress
    const totalSegments = progressStats?.length || 0;
    const completedSegments = progressStats?.filter(s => s.status === 'translated').length || 0;
    const reviewedSegments = progressStats?.filter(s => s.status === 'reviewed').length || 0;
    const approvedSegments = progressStats?.filter(s => s.status === 'approved').length || 0;
    const pendingSegments = progressStats?.filter(s => s.status === 'pending').length || 0;
    const inProgressSegments = progressStats?.filter(s => s.status === 'in_progress').length || 0;

    // Get terminology entries for this project
    const { data: terminology, error: terminologyError } = await supabase
      .from('terminology_entries')
      .select('*')
      .eq('project_id', projectId);

    if (terminologyError) {
      console.error('Terminology fetch error:', terminologyError);
    }

    // Get recent activity (project activity log) - without user join due to foreign key issues
    const { data: recentActivity, error: activityError } = await supabase
      .from('project_activity')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (activityError) {
      console.error('Activity fetch error:', activityError);
    }

    // Get user details separately for activity
    let activityWithUsers = [];
    if (recentActivity && recentActivity.length > 0) {
      const activityUserIds = [...new Set(recentActivity.map(activity => activity.user_id).filter(Boolean))];
      if (activityUserIds.length > 0) {
        const { data: activityUsers, error: activityUsersError } = await supabase
          .from('users')
          .select('id, name, avatar_url')
          .in('id', activityUserIds);

        if (!activityUsersError && activityUsers) {
          activityWithUsers = recentActivity.map(activity => ({
            ...activity,
            user: activityUsers.find(user => user.id === activity.user_id) || null
          }));
        } else {
          console.error('Activity users fetch error:', activityUsersError);
          activityWithUsers = recentActivity.map(activity => ({
            ...activity,
            user: null
          }));
        }
      } else {
        activityWithUsers = recentActivity;
      }
    }

    // Format the response with proper field name transformation
    const response = {
      project: {
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        priority: project.priority,
        sourceLanguage: project.source_language,
        targetLanguages: project.target_languages,
        documentType: project.document_type,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        dueDate: project.due_date,
        budget: project.budget,
        spent: project.spent || 0,
        createdBy: project.created_by,
        organizationId: project.organization_id,
        totalSegments,
        completedSegments,
        reviewedSegments,
        approvedSegments,
        teamMembers: [],
        files: [],
      },
      documents: documents || [],
      team: teamMembersWithUsers || [],
      progress: {
        totalSegments,
        pendingSegments,
        inProgressSegments,
        translatedSegments: completedSegments,
        reviewedSegments,
        approvedSegments,
      },
      terminology: terminology || [],
      recentActivity: activityWithUsers || [],
      success: true,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;
    const body = await request.json();

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // Verify user has permission to update this project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
      .single();

    if (!projectAccess) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Update the project
    const { data: project, error: updateError } = await supabase
      .from('projects')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', projectId)
      .select()
      .single();

    if (updateError) {
      console.error('Project update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update project', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      project,
      success: true,
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // Verify user has permission to delete this project (only creator or project manager)
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userId},project_manager_id.eq.${userId}`)
      .single();

    if (!projectAccess) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Delete the project (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (deleteError) {
      console.error('Project delete error:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete project', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
