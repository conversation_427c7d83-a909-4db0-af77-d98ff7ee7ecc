import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params before using
    const { projectId } = await params;

    const { searchParams } = new URL(request.url);
    const sourceText = searchParams.get('sourceText');
    const targetLanguage = searchParams.get('targetLanguage');
    const category = searchParams.get('category');
    const approvalStatus = searchParams.get('approvalStatus') || 'approved';
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');

    const supabase = createServiceClient();

    // Build the query for project-specific terminology
    let query = supabase
      .from('terminology_entries')
      .select(`
        *,
        created_by_user:users!terminology_entries_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!terminology_entries_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .eq('project_id', projectId);

    // Apply filters
    if (targetLanguage) {
      query = query.eq('target_language', targetLanguage);
    }

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (approvalStatus && approvalStatus !== 'all') {
      query = query.eq('approval_status', approvalStatus);
    }

    if (search) {
      query = query.or(`source_term.ilike.%${search}%,target_term.ilike.%${search}%`);
    }

    // If sourceText is provided, prioritize terms that appear in the text
    if (sourceText) {
      // First get all terms, then we'll sort them by relevance
      query = query.order('frequency', { ascending: false });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    query = query.limit(limit);

    const { data: terminology, error } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch project terminology', success: false },
        { status: 500 }
      );
    }

    // Transform the data and add highlighting information
    let transformedTerminology = terminology?.map(entry => ({
      id: entry.id,
      sourceTerm: entry.source_term,
      targetTerm: entry.target_term,
      targetLanguage: entry.target_language,
      category: entry.category,
      context: entry.context,
      usageNotes: entry.usage_notes,
      approvalStatus: entry.approval_status,
      frequency: entry.frequency || 0,
      createdBy: entry.created_by,
      reviewedBy: entry.reviewed_by,
      lastUsed: entry.last_used,
      createdAt: entry.created_at,
      updatedAt: entry.updated_at,
      organizationId: entry.organization_id,
      projectId: entry.project_id,
      highlighted: false, // Will be set based on sourceText matching
    })) || [];

    // If sourceText is provided, highlight terms that appear in the text
    if (sourceText && transformedTerminology.length > 0) {
      const sourceTextLower = sourceText.toLowerCase();
      
      transformedTerminology = transformedTerminology.map(term => ({
        ...term,
        highlighted: sourceTextLower.includes(term.sourceTerm.toLowerCase())
      }));

      // Sort highlighted terms first
      transformedTerminology.sort((a, b) => {
        if (a.highlighted && !b.highlighted) return -1;
        if (!a.highlighted && b.highlighted) return 1;
        return b.frequency - a.frequency; // Then by frequency
      });
    }

    return NextResponse.json({
      success: true,
      data: transformedTerminology,
      message: 'Project terminology fetched successfully',
      meta: {
        projectId: projectId,
        totalEntries: transformedTerminology.length,
        highlightedEntries: transformedTerminology.filter(t => t.highlighted).length,
        filters: {
          sourceText: sourceText || null,
          targetLanguage: targetLanguage || null,
          category: category || null,
          approvalStatus: approvalStatus || null,
          search: search || null,
        }
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
