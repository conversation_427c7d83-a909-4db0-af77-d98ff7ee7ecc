import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params before using
    const { id } = await params;

    const { data: entry, error } = await supabase
      .from('terminology_entries')
      .select(`
        *,
        created_by_user:users!terminology_entries_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!terminology_entries_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Terminology entry not found', success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: entry.id,
        sourceTerm: entry.source_term,
        targetTerm: entry.target_term,
        targetLanguage: entry.target_language,
        category: entry.category,
        context: entry.context,
        usageNotes: entry.usage_notes,
        approvalStatus: entry.approval_status,
        frequency: entry.frequency || 0,
        createdBy: entry.created_by,
        reviewedBy: entry.reviewed_by,
        lastUsed: entry.last_used,
        createdAt: entry.created_at,
        updatedAt: entry.updated_at,
        organizationId: entry.organization_id,
        projectId: entry.project_id,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      sourceTerm,
      targetTerm,
      targetLanguage,
      category,
      context,
      usageNotes,
      approvalStatus,
    } = body;

    // Build update object
    const updateData: any = {};
    if (sourceTerm !== undefined) updateData.source_term = sourceTerm;
    if (targetTerm !== undefined) updateData.target_term = targetTerm;
    if (targetLanguage !== undefined) updateData.target_language = targetLanguage;
    if (category !== undefined) updateData.category = category;
    if (context !== undefined) updateData.context = context;
    if (usageNotes !== undefined) updateData.usage_notes = usageNotes;
    if (approvalStatus !== undefined) {
      updateData.approval_status = approvalStatus;
      if (approvalStatus === 'approved' || approvalStatus === 'rejected') {
        updateData.reviewed_by = session.user.id;
      }
    }

    const { data: entry, error } = await supabase
      .from('terminology_entries')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to update terminology entry', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: entry.id,
        sourceTerm: entry.source_term,
        targetTerm: entry.target_term,
        targetLanguage: entry.target_language,
        category: entry.category,
        context: entry.context,
        usageNotes: entry.usage_notes,
        approvalStatus: entry.approval_status,
        frequency: entry.frequency || 0,
        createdBy: entry.created_by,
        reviewedBy: entry.reviewed_by,
        lastUsed: entry.last_used,
        createdAt: entry.created_at,
        updatedAt: entry.updated_at,
        organizationId: entry.organization_id,
        projectId: entry.project_id,
      },
      message: 'Terminology entry updated successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { error } = await supabase
      .from('terminology_entries')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to delete terminology entry', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Terminology entry deleted successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
