'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, BookOpen, Loader2, Edit, AlertTriangle } from 'lucide-react';
import { useGetSingleSeriesQuery } from '@/lib/api/series';
import { SeriesForm } from '@/components/series/SeriesForm';

export default function SeriesDetailPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;
  const seriesId = params.seriesId as string;
  const [isEditing, setIsEditing] = useState(false);

  const { data: seriesData, isLoading, error } = useGetSingleSeriesQuery(seriesId);

  const series = seriesData?.data?.chapter;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleEditSuccess = () => {
    setIsEditing(false);
    // Optionally refresh the data or show a success message
  };

  const handleEditCancel = () => {
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !series) {
    // Log error for debugging
    if (error) {
      console.error('Series fetch error:', error);
    }

    return (
      <div className="container mx-auto py-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="h-8 w-8 text-gray-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Series Not Found</h1>
            <p className="text-gray-600 mt-2">
              The series you're looking for doesn't exist or you don't have access to it.
            </p>
            {error && 'status' in error && error.status === 404 && (
              <p className="text-sm text-gray-500 mt-1">
                Series ID: {seriesId}
              </p>
            )}
          </div>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/projects/${projectId}`)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <Button
              onClick={() => router.push(`/dashboard/projects/${projectId}/series/new`)}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Create New Series
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If editing, show the form
  if (isEditing) {
    return (
      <SeriesForm
        projectId={projectId}
        series={series}
        onSuccess={handleEditSuccess}
        onCancel={handleEditCancel}
      />
    );
  }

  // View mode - show series details with edit button
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/projects/${projectId}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Series {series.chapter_number}: {series.title}</h1>
            <p className="text-gray-600">View and manage series details</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(series.status)}>
            {series.status.replace('_', ' ')}
          </Badge>
          <Button onClick={() => setIsEditing(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
        </div>
      </div>

      {/* Series Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Series Details
          </CardTitle>
          <CardDescription>
            View series information and progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Series Number</Label>
                <p className="text-sm font-medium">{series.chapter_number}</p>
              </div>
              <div className="space-y-2">
                <Label>Word Count</Label>
                <p className="text-sm font-medium">{series.source_word_count?.toLocaleString() || 0}</p>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Series Title</Label>
              <p className="text-sm font-medium">{series.title}</p>
            </div>

            <div className="space-y-2">
              <Label>Description</Label>
              <div className="text-sm text-gray-600 prose prose-sm max-w-none">
                {series.description ? (
                  <div dangerouslySetInnerHTML={{ __html: series.description }} />
                ) : (
                  <p className="italic">No description provided</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
              <div>
                <Label>Progress</Label>
                <p className="text-sm font-medium">{Math.round(series.progress_percentage || 0)}%</p>
              </div>
              <div>
                <Label>Estimated Cost</Label>
                <p className="text-sm font-medium">{series.estimated_cost || 0} credits</p>
              </div>
              <div>
                <Label>Actual Cost</Label>
                <p className="text-sm font-medium">{series.actual_cost || 0} credits</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
