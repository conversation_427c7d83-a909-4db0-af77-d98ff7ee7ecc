'use client';

import { useState } from 'react';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ProjectForm } from './ProjectForm';

export const projectFormSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  sourceLanguage: z.string().min(1, 'Source language is required'),
  targetLanguages: z.array(z.string()).min(1, 'At least one target language is required'),
  deadline: z.date().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  documentType: z.string().min(1, 'Document type is required'),
});

export type ProjectFormData = z.infer<typeof projectFormSchema>;

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ProjectFormData) => void;
}

export const languages = [
  'English',
  'Spanish',
  'French',
  'German',
  'Italian',
  'Portuguese',
  'Russian',
  'Chinese (Simplified)',
  'Chinese (Traditional)',
  'Japanese',
  'Korean',
  'Arabic',
  'Dutch',
  'Swedish',
  'Norwegian',
];

export const documentTypes = [
  { value: 'novel', label: 'Novel/Fiction' },
  { value: 'game', label: 'Game Content' },
  { value: 'technical', label: 'Technical Documentation' },
  { value: 'marketing', label: 'Marketing Materials' },
  { value: 'general', label: 'General Content' },
];

export const priorities = [
  { value: 'low', label: 'Low', color: 'text-gray-600' },
  { value: 'medium', label: 'Medium', color: 'text-blue-600' },
  { value: 'high', label: 'High', color: 'text-orange-600' },
  { value: 'urgent', label: 'Urgent', color: 'text-red-600' },
];

export function CreateProjectDialog({ open, onOpenChange, onSubmit }: CreateProjectDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleFormSubmit = async (data: ProjectFormData) => {
    setIsLoading(true);
    try {
      await onSubmit(data);
      onOpenChange(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Set up a new translation project with source and target languages.
          </DialogDescription>
        </DialogHeader>

        <ProjectForm
          onSubmit={handleFormSubmit}
          onCancel={handleClose}
          isLoading={isLoading}
          submitButtonText="Create Project"
        />
      </DialogContent>
    </Dialog>
  );
}
