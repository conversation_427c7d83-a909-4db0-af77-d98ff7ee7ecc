import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ seriesId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params before using
    const { seriesId } = await params;

    const { searchParams } = new URL(request.url);
    const targetLanguage = searchParams.get('targetLanguage');
    const category = searchParams.get('category');
    const approvalStatus = searchParams.get('approvalStatus') || 'approved';
    const includeUsage = searchParams.get('includeUsage') === 'true';

    const supabase = createServiceClient();

    // First, get all projects in the series
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, title')
      .eq('series_id', seriesId);

    if (projectsError) {
      console.error('Error fetching series projects:', projectsError);
      return NextResponse.json(
        { error: 'Failed to fetch series projects', success: false },
        { status: 500 }
      );
    }

    if (!projects || projects.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        message: 'No projects found in series',
        meta: {
          seriesId: seriesId,
          projectCount: 0,
          totalTerms: 0,
        }
      });
    }

    const projectIds = projects.map(p => p.id);

    // Build terminology query
    let query = supabase
      .from('terminology_entries')
      .select(`
        *,
        created_by_user:users!terminology_entries_created_by_fkey(
          id,
          name,
          email
        ),
        reviewed_by_user:users!terminology_entries_reviewed_by_fkey(
          id,
          name,
          email
        )
      `)
      .in('project_id', projectIds);

    // Apply filters
    if (targetLanguage) {
      query = query.eq('target_language', targetLanguage);
    }

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (approvalStatus && approvalStatus !== 'all') {
      query = query.eq('approval_status', approvalStatus);
    }

    query = query.order('frequency', { ascending: false });

    const { data: terminology, error } = await query;

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch series terminology', success: false },
        { status: 500 }
      );
    }

    // Group terminology by source term to identify conflicts
    const terminologyMap = new Map<string, any[]>();
    terminology?.forEach(term => {
      const key = `${term.source_term}_${term.target_language}`;
      if (!terminologyMap.has(key)) {
        terminologyMap.set(key, []);
      }
      terminologyMap.get(key)!.push({
        ...term,
        projectTitle: projects.find(p => p.id === term.project_id)?.title || 'Unknown Project'
      });
    });

    // Identify conflicts and create series-level terminology
    const seriesTerminology: any[] = [];
    const conflicts: any[] = [];

    terminologyMap.forEach((terms, key) => {
      if (terms.length === 1) {
        // No conflict, single term
        seriesTerminology.push({
          ...terms[0],
          projectUsage: [{
            projectId: terms[0].project_id,
            projectTitle: terms[0].projectTitle,
            frequency: terms[0].frequency || 0,
            lastUsed: terms[0].last_used,
            consistency: 1.0,
          }],
          isConsistent: true,
        });
      } else {
        // Multiple translations for same source term
        const uniqueTranslations = new Set(terms.map(t => t.target_term));
        
        if (uniqueTranslations.size === 1) {
          // Same translation across projects - consistent
          const masterTerm = terms[0];
          seriesTerminology.push({
            ...masterTerm,
            projectUsage: terms.map(t => ({
              projectId: t.project_id,
              projectTitle: t.projectTitle,
              frequency: t.frequency || 0,
              lastUsed: t.last_used,
              consistency: 1.0,
            })),
            isConsistent: true,
            totalFrequency: terms.reduce((sum, t) => sum + (t.frequency || 0), 0),
          });
        } else {
          // Different translations - conflict
          const mostFrequent = terms.reduce((max, term) => 
            (term.frequency || 0) > (max.frequency || 0) ? term : max
          );

          seriesTerminology.push({
            ...mostFrequent,
            projectUsage: terms.map(t => ({
              projectId: t.project_id,
              projectTitle: t.projectTitle,
              frequency: t.frequency || 0,
              lastUsed: t.last_used,
              consistency: t.target_term === mostFrequent.target_term ? 1.0 : 0.0,
            })),
            isConsistent: false,
            totalFrequency: terms.reduce((sum, t) => sum + (t.frequency || 0), 0),
            conflictingTranslations: Array.from(uniqueTranslations),
          });

          conflicts.push({
            sourceTerm: mostFrequent.source_term,
            targetLanguage: mostFrequent.target_language,
            translations: terms.map(t => ({
              projectId: t.project_id,
              projectTitle: t.projectTitle,
              translation: t.target_term,
              frequency: t.frequency || 0,
            })),
            recommendedTranslation: mostFrequent.target_term,
          });
        }
      }
    });

    // Calculate consistency metrics
    const totalTerms = seriesTerminology.length;
    const consistentTerms = seriesTerminology.filter(t => t.isConsistent).length;
    const consistencyScore = totalTerms > 0 ? consistentTerms / totalTerms : 1.0;

    return NextResponse.json({
      success: true,
      data: seriesTerminology,
      message: 'Series terminology fetched successfully',
      meta: {
        seriesId: params.seriesId,
        projectCount: projects.length,
        totalTerms,
        consistentTerms,
        inconsistentTerms: totalTerms - consistentTerms,
        consistencyScore,
        conflicts: conflicts.length,
        filters: {
          targetLanguage: targetLanguage || null,
          category: category || null,
          approvalStatus: approvalStatus || null,
        }
      },
      conflicts: conflicts.length > 0 ? conflicts : undefined,
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ seriesId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Await params before using
    const { seriesId } = await params;

    const body = await request.json();
    const { action, ...actionData } = body;

    const supabase = createServiceClient();

    switch (action) {
      case 'sync_terminology':
        return await handleSyncTerminology(supabase, seriesId, actionData);

      case 'resolve_conflict':
        return await handleResolveConflict(supabase, seriesId, actionData);

      case 'propagate_change':
        return await handlePropagateChange(supabase, seriesId, actionData);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action', success: false },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        success: false,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function handleSyncTerminology(supabase: any, seriesId: string, data: any) {
  // Implementation for syncing terminology across series projects
  return NextResponse.json({
    success: true,
    data: { synced: 0, skipped: 0, conflicts: [] },
    message: 'Terminology sync completed',
  });
}

async function handleResolveConflict(supabase: any, seriesId: string, data: any) {
  // Implementation for resolving terminology conflicts
  return NextResponse.json({
    success: true,
    data: { resolved: true },
    message: 'Conflict resolved successfully',
  });
}

async function handlePropagateChange(supabase: any, seriesId: string, data: any) {
  // Implementation for propagating terminology changes
  return NextResponse.json({
    success: true,
    data: { updated: 0, failed: 0, errors: [] },
    message: 'Changes propagated successfully',
  });
}
