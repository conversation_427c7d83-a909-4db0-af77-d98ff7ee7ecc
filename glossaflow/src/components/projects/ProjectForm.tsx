'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2, Save, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import {
  projectFormSchema,
  ProjectFormData,
  languages,
  documentTypes,
  priorities
} from './CreateProjectDialog';
import type { Project } from '@/lib/api/projects';

interface ProjectFormProps {
  project?: Project; // If provided, we're editing; if not, we're creating
  onSubmit: (data: ProjectFormData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  submitButtonText?: string;
}

export function ProjectForm({ 
  project, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  submitButtonText 
}: ProjectFormProps) {
  const isEditing = !!project;
  const [selectedTargetLanguages, setSelectedTargetLanguages] = useState<string[]>(
    project?.targetLanguages || []
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      name: project?.name || '',
      description: project?.description || '',
      sourceLanguage: project?.sourceLanguage || '',
      targetLanguages: project?.targetLanguages || [],
      deadline: project?.dueDate ? new Date(project.dueDate) : undefined,
      priority: project?.priority || 'medium',
      documentType: (project as any)?.documentType || (project as any)?.document_type || '', // Handle both camelCase and snake_case
    },
  });

  const sourceLanguage = watch('sourceLanguage');
  const deadline = watch('deadline');
  const priority = watch('priority');

  // Update form when project changes (for editing)
  useEffect(() => {
    if (project) {
      reset({
        name: project.name,
        description: project.description || '',
        sourceLanguage: project.sourceLanguage,
        targetLanguages: project.targetLanguages,
        deadline: project.dueDate ? new Date(project.dueDate) : undefined,
        priority: project.priority,
        documentType: (project as any)?.documentType || (project as any)?.document_type || '',
      });
      setSelectedTargetLanguages(project.targetLanguages);
    }
  }, [project, reset]);

  const availableTargetLanguages = languages.filter(
    (language) => language !== sourceLanguage && !selectedTargetLanguages.includes(language)
  );

  const addTargetLanguage = (language: string) => {
    if (!selectedTargetLanguages.includes(language) && language !== sourceLanguage) {
      const newLanguages = [...selectedTargetLanguages, language];
      setSelectedTargetLanguages(newLanguages);
      setValue('targetLanguages', newLanguages);
    }
  };

  const removeTargetLanguage = (language: string) => {
    const newLanguages = selectedTargetLanguages.filter((lang) => lang !== language);
    setSelectedTargetLanguages(newLanguages);
    setValue('targetLanguages', newLanguages);
  };

  const handleFormSubmit = async (data: ProjectFormData) => {
    await onSubmit({
      ...data,
      targetLanguages: selectedTargetLanguages,
    });
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Project Name */}
      <div className="space-y-2">
        <Label htmlFor="name">Project Name *</Label>
        <Input
          id="name"
          placeholder="Enter project name"
          {...register('name')}
          disabled={isLoading}
        />
        {errors.name && (
          <p className="text-sm text-red-600">{errors.name.message}</p>
        )}
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          placeholder="Describe your project..."
          {...register('description')}
          disabled={isLoading}
        />
      </div>

      {/* Languages */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Source Language *</Label>
          <Select 
            value={sourceLanguage}
            onValueChange={(value) => setValue('sourceLanguage', value)}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select source language" />
            </SelectTrigger>
            <SelectContent>
              {languages.map((language) => (
                <SelectItem key={language} value={language}>
                  {language}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.sourceLanguage && (
            <p className="text-sm text-red-600">{errors.sourceLanguage.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Target Languages *</Label>
          <Select onValueChange={addTargetLanguage} disabled={isLoading}>
            <SelectTrigger>
              <SelectValue placeholder="Add target language" />
            </SelectTrigger>
            <SelectContent>
              {availableTargetLanguages.map((language) => (
                <SelectItem key={language} value={language}>
                  {language}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedTargetLanguages.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {selectedTargetLanguages.map((language) => (
                <div
                  key={language}
                  className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm"
                >
                  {language}
                  <button
                    type="button"
                    onClick={() => removeTargetLanguage(language)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                    disabled={isLoading}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
          {errors.targetLanguages && (
            <p className="text-sm text-red-600">{errors.targetLanguages.message}</p>
          )}
        </div>
      </div>

      {/* Document Type and Priority */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Document Type *</Label>
          <Select
            value={watch('documentType')}
            onValueChange={(value) => setValue('documentType', value)}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              {documentTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.documentType && (
            <p className="text-sm text-red-600">{errors.documentType.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Priority</Label>
          <Select 
            value={priority}
            onValueChange={(value) => setValue('priority', value as any)}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {priorities.map((priority) => (
                <SelectItem key={priority.value} value={priority.value}>
                  <span className={priority.color}>{priority.label}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Deadline */}
      <div className="space-y-2">
        <Label>Deadline (Optional)</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal',
                !deadline && 'text-muted-foreground'
              )}
              disabled={isLoading}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {deadline ? format(deadline, 'PPP') : 'Pick a date'}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={deadline}
              onSelect={(date) => setValue('deadline', date)}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>
              {isEditing ? (
                <Save className="mr-2 h-4 w-4" />
              ) : (
                <Plus className="mr-2 h-4 w-4" />
              )}
              {submitButtonText || (isEditing ? 'Update Project' : 'Create Project')}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
